#!/usr/bin/env python3
"""
Streamlit Web Application for Ultra-Fast Image Generator
Beautiful, modern UI with dynamic controls and real-time feedback
"""

import streamlit as st
import requests
import time
from PIL import Image
import io
import base64
from typing import Dict, Any, Optional
import json

# Page configuration
st.set_page_config(
    page_title="⚡ Ultra-Fast Image Generator",
    page_icon="🎨",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for modern styling
st.markdown("""
<style>
    .main-header {
        text-align: center;
        padding: 2rem 0;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    
    .metric-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #667eea;
        margin: 0.5rem 0;
    }
    
    .preset-button {
        width: 100%;
        margin: 0.25rem 0;
    }
    
    .generation-info {
        background: #e8f5e8;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #28a745;
    }
    
    .error-info {
        background: #f8d7da;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #dc3545;
    }
    
    .stButton > button {
        width: 100%;
        border-radius: 8px;
        border: none;
        padding: 0.5rem 1rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
</style>
""", unsafe_allow_html=True)

# API Configuration
API_BASE_URL = "http://localhost:8000"

class ImageGeneratorApp:
    def __init__(self):
        self.api_url = API_BASE_URL
        self.system_info = None
        self.models = []
        
    def check_api_connection(self):
        """Check if API is running"""
        try:
            response = requests.get(f"{self.api_url}/", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_system_info(self):
        """Get system information from API"""
        try:
            response = requests.get(f"{self.api_url}/system-info")
            if response.status_code == 200:
                self.system_info = response.json()
                return True
        except:
            pass
        return False
    
    def get_models(self):
        """Get available models from API"""
        try:
            response = requests.get(f"{self.api_url}/models")
            if response.status_code == 200:
                self.models = response.json()
                return True
        except:
            pass
        return False
    
    def generate_image(self, config: Dict[str, Any]):
        """Generate image using API"""
        try:
            response = requests.post(f"{self.api_url}/generate", json=config)
            return response.json()
        except Exception as e:
            return {"success": False, "error_message": str(e)}
    
    def get_image_url(self, image_id: str):
        """Get image download URL"""
        return f"{self.api_url}/image/{image_id}"

def main():
    app = ImageGeneratorApp()
    
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>⚡ Ultra-Fast Image Generator</h1>
        <p>AI-powered image generation optimized for low VRAM GPUs</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Check API connection
    if not app.check_api_connection():
        st.error("🔌 **API Connection Failed**")
        st.markdown("""
        **Please start the FastAPI backend first:**
        ```bash
        python api.py
        ```
        Then refresh this page.
        """)
        st.stop()
    
    # Get system info and models
    if not app.get_system_info() or not app.get_models():
        st.error("❌ Failed to load system information")
        st.stop()
    
    # Sidebar - System Info
    with st.sidebar:
        st.header("🖥️ System Info")
        
        if app.system_info:
            st.markdown(f"""
            <div class="metric-card">
                <strong>GPU:</strong> {app.system_info['gpu_name']}<br>
                <strong>VRAM:</strong> {app.system_info['total_vram_gb']:.1f} GB<br>
                <strong>Models Available:</strong> {len(app.models)}
            </div>
            """, unsafe_allow_html=True)
        
        st.header("🎛️ Generation Settings")
        
        # Model Selection
        if app.models:
            model_options = {model['display_name']: model['name'] for model in app.models}
            selected_model_display = st.selectbox(
                "🤖 Model",
                options=list(model_options.keys()),
                help="Choose the AI model for generation"
            )
            selected_model = model_options[selected_model_display]
            
            # Get selected model config
            model_config = next(m for m in app.models if m['name'] == selected_model)
            
            st.info(f"📝 {model_config['description']}")
        
        # Speed Presets
        st.subheader("🚀 Speed Presets")
        
        preset_cols = st.columns(1)
        with preset_cols[0]:
            if st.button("⚡ Fast", help="1-4 steps, maximum speed"):
                st.session_state.preset = "fast"
            if st.button("⚖️ Medium", help="8-15 steps, balanced"):
                st.session_state.preset = "medium"
            if st.button("🎯 Best Quality", help="20-50 steps, maximum quality"):
                st.session_state.preset = "best"
        
        # Apply preset settings
        if 'preset' in st.session_state and model_config:
            preset = st.session_state.preset
            optimal_steps = model_config['optimal_steps'][preset]
            
            if preset == "fast":
                default_steps = optimal_steps
                default_guidance = 0.0 if not model_config['supports_guidance'] else 3.0
            elif preset == "medium":
                default_steps = optimal_steps
                default_guidance = model_config['default_guidance']
            else:  # best
                default_steps = optimal_steps
                default_guidance = model_config['default_guidance']
        else:
            default_steps = 1
            default_guidance = 0.0
        
        # Manual Controls
        st.subheader("🔧 Manual Controls")
        
        num_steps = st.slider(
            "Inference Steps",
            min_value=1,
            max_value=50,
            value=default_steps,
            help="More steps = better quality but slower"
        )
        
        if model_config and model_config['supports_guidance']:
            guidance_scale = st.slider(
                "Guidance Scale",
                min_value=0.0,
                max_value=20.0,
                value=default_guidance,
                step=0.5,
                help="Higher values follow prompt more closely"
            )
        else:
            guidance_scale = 0.0
            st.info("ℹ️ This model doesn't use guidance scale")
        
        # Advanced Settings
        with st.expander("⚙️ Advanced Settings"):
            width = st.slider("Width", 256, model_config['max_resolution'], 512, 64)
            height = st.slider("Height", 256, model_config['max_resolution'], 512, 64)
            
            seed = st.number_input(
                "Seed (optional)",
                min_value=0,
                max_value=2147483647,
                value=None,
                help="Use same seed for reproducible results"
            )
            
            safety_checker = st.checkbox(
                "Enable Safety Checker",
                value=False,
                help="Filter potentially inappropriate content"
            )
    
    # Main content area
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("✍️ Create Your Image")
        
        # Prompt input
        prompt = st.text_area(
            "Describe your image:",
            placeholder="A beautiful sunset over mountains, photorealistic, 8k...",
            height=100,
            help="Be descriptive for better results"
        )
        
        negative_prompt = st.text_input(
            "Negative prompt (optional):",
            placeholder="blurry, low quality, distorted...",
            help="Describe what you don't want in the image"
        )
        
        # Generate button
        generate_col1, generate_col2 = st.columns([3, 1])
        
        with generate_col1:
            generate_button = st.button(
                "🎨 Generate Image",
                type="primary",
                disabled=not prompt.strip()
            )
        
        with generate_col2:
            if 'preset' in st.session_state:
                st.info(f"Mode: {st.session_state.preset.title()}")
    
    with col2:
        st.header("📊 Generation Info")
        
        # Current settings preview
        if model_config:
            st.markdown(f"""
            <div class="metric-card">
                <strong>Model:</strong> {model_config['display_name']}<br>
                <strong>Steps:</strong> {num_steps}<br>
                <strong>Guidance:</strong> {guidance_scale}<br>
                <strong>Resolution:</strong> {width}×{height}<br>
                <strong>Est. Time:</strong> {num_steps * 0.5:.1f}s
            </div>
            """, unsafe_allow_html=True)
    
    # Generation logic
    if generate_button and prompt.strip():
        # Prepare configuration
        config = {
            "prompt": prompt.strip(),
            "negative_prompt": negative_prompt.strip() if negative_prompt.strip() else None,
            "num_inference_steps": num_steps,
            "guidance_scale": guidance_scale,
            "width": width,
            "height": height,
            "seed": seed,
            "safety_checker": safety_checker,
            "model_name": selected_model
        }
        
        # Show generation progress
        with st.spinner("🎨 Generating your image..."):
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            # Simulate progress (since we can't get real-time progress from API)
            for i in range(num_steps):
                progress_bar.progress((i + 1) / num_steps)
                status_text.text(f"Step {i + 1}/{num_steps}")
                time.sleep(0.1)  # Small delay for visual effect
            
            # Call API
            result = app.generate_image(config)
        
        # Clear progress indicators
        progress_bar.empty()
        status_text.empty()
        
        # Display results
        if result.get("success"):
            st.success("🎉 **Image Generated Successfully!**")
            
            # Show generation metrics
            st.markdown(f"""
            <div class="generation-info">
                <strong>⏱️ Generation Time:</strong> {result['generation_time']:.2f} seconds<br>
                <strong>💾 VRAM Used:</strong> {result['vram_usage']:.2f} GB<br>
                <strong>🔧 Steps Used:</strong> {result['config_used']['num_inference_steps']}
            </div>
            """, unsafe_allow_html=True)
            
            # Display image
            image_url = app.get_image_url(result['image_id'])
            
            try:
                response = requests.get(image_url)
                if response.status_code == 200:
                    image = Image.open(io.BytesIO(response.content))
                    st.image(image, caption=f"Generated: {prompt[:50]}...", use_column_width=True)
                    
                    # Download button
                    st.download_button(
                        label="📥 Download Image",
                        data=response.content,
                        file_name=f"generated_{result['image_id']}.png",
                        mime="image/png"
                    )
            except Exception as e:
                st.error(f"Failed to load image: {e}")
        
        else:
            st.error("❌ **Generation Failed**")
            if result.get("error_message"):
                st.markdown(f"""
                <div class="error-info">
                    <strong>Error:</strong> {result['error_message']}
                </div>
                """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
