import torch
from diffusers import DiffusionPipeline

# Check GPU availability
if torch.cuda.is_available():
    device = "cuda"
    print(f"Using GPU: {torch.cuda.get_device_name()}")
    print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
else:
    device = "cpu"
    print("Using CPU")

# Use SD 1.5 Turbo for low VRAM
pipe = DiffusionPipeline.from_pretrained(
    "stabilityai/sd-turbo",
    torch_dtype=torch.float16,
    variant="fp16"
)
pipe = pipe.to(device)

# Enable memory efficient attention
pipe.enable_attention_slicing()
pipe.enable_model_cpu_offload()


while True:
    prompt = input("Enter a prompt (or 'exit' to quit): ")
    if prompt.lower() == 'exit':
        break

    # Generate image
    image = pipe(prompt).images[0]
    
    # Save and show the image
    image.save("output_image.png")
    image.show()
    print("Image generated and saved as 'output_image.png'.")