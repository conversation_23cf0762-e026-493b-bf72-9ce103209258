import torch
from diffusers import DiffusionPipeline

# Check GPU availability
if torch.cuda.is_available():
    device = "cuda"
    print(f"Using GPU: {torch.cuda.get_device_name()}")
    print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
else:
    device = "cpu"
    print("Using CPU")

# Use SD 1.5 Turbo for low VRAM
pipe = DiffusionPipeline.from_pretrained(
    "stabilityai/sd-turbo",
    torch_dtype=torch.float16,
    variant="fp16"
)
pipe = pipe.to(device)

# Enable memory efficient attention
pipe.enable_attention_slicing()
pipe.enable_model_cpu_offload()

prompt = "Astronaut in a jungle, cold color palette, muted colors, detailed, 8k"
image = pipe(prompt, num_inference_steps=1, guidance_scale=0.0).images[0]
image.save("astronaut.png")
