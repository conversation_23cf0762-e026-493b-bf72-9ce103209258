#!/bin/bash

# Ultra-Fast Image Generator - Backend Startup Script
# This script starts the FastAPI backend using uv

echo "🚀 Starting Ultra-Fast Image Generator API Backend..."
echo "📡 API will be available at: http://localhost:8000"
echo "📚 API docs will be available at: http://localhost:8000/docs"
echo "🔄 Press Ctrl+C to stop the server"
echo "=================================================="

# Check if uv is installed
if ! command -v uv &> /dev/null; then
    echo "❌ uv is not installed. Please install it first:"
    echo "   curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

# Install dependencies if needed
echo "📦 Installing dependencies..."
uv sync

# Start the FastAPI server
echo "🚀 Starting FastAPI server..."
uv run fastapi run api.py --reload --port 8000 --host 0.0.0.0
