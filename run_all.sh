#!/bin/bash

# Ultra-Fast Image Generator - Complete Startup Script
# This script starts both backend and frontend

echo "🚀 Ultra-Fast Image Generator - Complete Setup"
echo "=============================================="

# Check if uv is installed
if ! command -v uv &> /dev/null; then
    echo "❌ uv is not installed. Please install it first:"
    echo "   curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

# Install dependencies
echo "📦 Installing all dependencies..."
uv sync

# Function to cleanup background processes
cleanup() {
    echo ""
    echo "🛑 Shutting down services..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
        echo "✅ Backend stopped"
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
        echo "✅ Frontend stopped"
    fi
    echo "👋 Goodbye!"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start backend in background
echo "🚀 Starting backend..."
uv run fastapi run api.py --reload --port 8000 --host 0.0.0.0 &
BACKEND_PID=$!

# Wait a moment for backend to start
echo "⏳ Waiting for backend to start..."
sleep 5

# Check if backend is running
if curl -s http://localhost:8000/ > /dev/null 2>&1; then
    echo "✅ Backend is running at http://localhost:8000"
else
    echo "❌ Backend failed to start"
    cleanup
fi

# Start frontend in background
echo "🎨 Starting frontend..."
uv run streamlit run app.py --server.port 8501 --server.address 0.0.0.0 --browser.gatherUsageStats false &
FRONTEND_PID=$!

# Wait a moment for frontend to start
sleep 3

echo ""
echo "🎉 Ultra-Fast Image Generator is ready!"
echo "=================================================="
echo "🔗 Backend API: http://localhost:8000"
echo "📚 API Docs: http://localhost:8000/docs"
echo "🌐 Web App: http://localhost:8501"
echo "=================================================="
echo "🔄 Press Ctrl+C to stop both services"
echo ""

# Wait for user interrupt
wait
