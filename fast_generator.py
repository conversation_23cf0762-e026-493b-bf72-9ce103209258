#!/usr/bin/env python3
"""
Ultra-Fast Image Generator - Stable Version
Optimized for maximum speed on 4-6GB VRAM GPUs
"""

import torch
import time
from datetime import datetime
from diffusers import AutoPipelineForText2Image

class FastImageGenerator:
    def __init__(self):
        self.pipeline = None
        self.model_name = None
        
    def setup(self):
        """Initialize the fastest stable model"""
        print("🚀 Ultra-Fast Image Generator")
        print("=" * 50)
        
        if not torch.cuda.is_available():
            print("❌ CUDA not available. GPU required.")
            return False
        
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"🎮 GPU: {torch.cuda.get_device_name(0)}")
        print(f"💾 VRAM: {gpu_memory:.1f} GB")
        
        # Try SD-Turbo first (fastest)
        if self._load_sd_turbo():
            return True
        
        # Fallback to SDXL-Turbo
        if self._load_sdxl_turbo():
            return True
            
        print("❌ Failed to load any fast model")
        return False
    
    def _load_sd_turbo(self):
        """Load SD-Turbo (fastest, 4GB+ VRAM)"""
        try:
            print("\n⚡ Loading SD-Turbo (ultra-fast)...")
            self.pipeline = AutoPipelineForText2Image.from_pretrained(
                "stabilityai/sd-turbo",
                torch_dtype=torch.float16,
                variant="fp16",
                use_safetensors=True
            )
            
            # Simple optimizations that work reliably
            self.pipeline.enable_vae_slicing()
            self.pipeline.enable_attention_slicing()
            
            # Move to GPU
            self.pipeline = self.pipeline.to("cuda")
            
            self.model_name = "SD-Turbo"
            print("✅ SD-Turbo loaded successfully")
            return True
            
        except Exception as e:
            print(f"⚠️  SD-Turbo failed: {e}")
            return False
    
    def _load_sdxl_turbo(self):
        """Load SDXL-Turbo as fallback"""
        try:
            print("\n⚡ Loading SDXL-Turbo (fallback)...")
            self.pipeline = AutoPipelineForText2Image.from_pretrained(
                "stabilityai/sdxl-turbo",
                torch_dtype=torch.float16,
                variant="fp16",
                use_safetensors=True
            )
            
            self.pipeline.enable_vae_slicing()
            self.pipeline.enable_attention_slicing()
            self.pipeline = self.pipeline.to("cuda")
            
            self.model_name = "SDXL-Turbo"
            print("✅ SDXL-Turbo loaded successfully")
            return True
            
        except Exception as e:
            print(f"⚠️  SDXL-Turbo failed: {e}")
            return False
    
    def generate(self, prompt, filename=None):
        """Generate image with maximum speed"""
        if self.pipeline is None:
            print("❌ No model loaded")
            return None
        
        print(f"\n🎨 Generating: '{prompt}'")
        print(f"⚡ Using {self.model_name} (1-step generation)")
        
        # Clear cache
        torch.cuda.empty_cache()
        
        start_time = time.time()
        
        try:
            with torch.inference_mode():
                # Ultra-fast settings
                image = self.pipeline(
                    prompt=prompt,
                    num_inference_steps=1,      # Single step for max speed
                    guidance_scale=0.0,         # No guidance for turbo models
                    height=512,                 # Optimal size
                    width=512,
                    generator=torch.Generator(device="cuda").manual_seed(42)
                ).images[0]
            
            # Calculate metrics
            generation_time = time.time() - start_time
            memory_used = torch.cuda.max_memory_allocated() / 1024**3
            
            # Save image
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"generated_{timestamp}.png"
            
            image.save(filename)
            
            # Report results
            print(f"✅ Generated in {generation_time:.2f} seconds")
            print(f"💾 Peak VRAM: {memory_used:.2f} GB")
            print(f"📁 Saved: {filename}")
            
            return image, generation_time, memory_used
            
        except Exception as e:
            print(f"❌ Generation failed: {e}")
            return None
    
    def interactive_mode(self):
        """Interactive prompt loop"""
        print("\n🎯 Interactive Mode Ready!")
        print("💡 Tips:")
        print("  - Keep prompts simple for best speed")
        print("  - Type 'quit' or 'exit' to stop")
        print("  - Press Ctrl+C to interrupt")
        print()
        
        while True:
            try:
                prompt = input("🎨 Enter prompt: ").strip()
                
                if prompt.lower() in ['quit', 'exit', 'q', '']:
                    print("👋 Goodbye!")
                    break
                
                result = self.generate(prompt)
                if result:
                    print("🎉 Success!\n")
                else:
                    print("❌ Failed. Try again.\n")
                    
            except KeyboardInterrupt:
                print("\n👋 Interrupted. Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}\n")

def main():
    """Main function"""
    generator = FastImageGenerator()
    
    if not generator.setup():
        print("❌ Setup failed. Check your GPU and internet connection.")
        return
    
    print("\n" + "=" * 50)
    print("🚀 READY FOR ULTRA-FAST GENERATION!")
    print("=" * 50)
    
    # Quick test
    print("\n🧪 Running quick test...")
    test_result = generator.generate("a beautiful landscape")
    
    if test_result:
        print("✅ Test successful! Starting interactive mode...")
        generator.interactive_mode()
    else:
        print("❌ Test failed. Please check your setup.")

if __name__ == "__main__":
    main()
