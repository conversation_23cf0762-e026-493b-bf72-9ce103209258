#!/usr/bin/env python3
"""
Quick Test - Ultra-Fast Image Generation
Minimal script for immediate testing
"""

import torch
import time
from diffusers import AutoPipelineForText2Image

def quick_generate():
    """Quick test generation"""
    print("🚀 Quick Test - Ultra-Fast Image Generator")
    
    # Check GPU
    if not torch.cuda.is_available():
        print("❌ No GPU available")
        return
    
    print(f"🎮 GPU: {torch.cuda.get_device_name(0)}")
    
    try:
        # Load fastest model
        print("⚡ Loading SD-Turbo...")
        pipeline = AutoPipelineForText2Image.from_pretrained(
            "stabilityai/sd-turbo",
            torch_dtype=torch.float16,
            variant="fp16"
        )
        
        # Optimize for speed
        pipeline.enable_model_cpu_offload()
        pipeline.enable_vae_slicing()
        
        print("✅ Model loaded!")
        
        # Quick generation
        prompt = "a beautiful sunset over mountains"
        print(f"🎨 Generating: {prompt}")
        
        start = time.time()
        image = pipeline(
            prompt,
            num_inference_steps=1,
            guidance_scale=0.0
        ).images[0]
        
        duration = time.time() - start
        memory = torch.cuda.max_memory_allocated() / 1024**3
        
        # Save
        image.save("quick_test_output.png")
        
        print(f"✅ Done in {duration:.2f}s")
        print(f"💾 VRAM: {memory:.2f}GB")
        print("📁 Saved: quick_test_output.png")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    quick_generate()
