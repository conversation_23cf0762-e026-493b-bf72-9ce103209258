#!/usr/bin/env python3
"""
FastAPI Backend for Ultra-Fast Image Generator
Optimized for low VRAM GPUs with multiple model support
"""

from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from pydantic import BaseModel, <PERSON>
from typing import Optional, Dict, Any, List
import torch
import time
import uuid
import os
from datetime import datetime
from diffusers import AutoPipelineForText2Image, StableDiffusionXLPipeline
import base64
from io import BytesIO
from PIL import Image
import asyncio
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Ultra-Fast Image Generator API",
    description="High-speed image generation optimized for low VRAM GPUs",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global pipeline storage
pipelines = {}
current_model = None

class GenerationConfig(BaseModel):
    """Configuration for image generation"""
    prompt: str = Field(..., description="Text prompt for image generation")
    negative_prompt: Optional[str] = Field(None, description="Negative prompt")
    num_inference_steps: int = Field(1, ge=1, le=50, description="Number of inference steps")
    guidance_scale: float = Field(0.0, ge=0.0, le=20.0, description="Guidance scale")
    width: int = Field(512, ge=256, le=1024, description="Image width")
    height: int = Field(512, ge=256, le=1024, description="Image height")
    seed: Optional[int] = Field(None, description="Random seed for reproducibility")
    safety_checker: bool = Field(False, description="Enable safety checker")
    model_name: str = Field("sd-turbo", description="Model to use")

class ModelConfig(BaseModel):
    """Model configuration and capabilities"""
    name: str
    display_name: str
    description: str
    min_vram_gb: float
    max_resolution: int
    optimal_steps: Dict[str, int]  # fast, medium, best
    supports_guidance: bool
    default_guidance: float

class GenerationResponse(BaseModel):
    """Response from image generation"""
    success: bool
    image_id: str
    generation_time: float
    vram_usage: float
    config_used: GenerationConfig
    error_message: Optional[str] = None

class SystemInfo(BaseModel):
    """System information"""
    gpu_name: str
    total_vram_gb: float
    available_models: List[ModelConfig]
    current_model: Optional[str]

# Model configurations
MODEL_CONFIGS = {
    "sd-turbo": ModelConfig(
        name="sd-turbo",
        display_name="SD-Turbo",
        description="Ultra-fast 1-step generation (512x512)",
        min_vram_gb=3.0,
        max_resolution=512,
        optimal_steps={"fast": 1, "medium": 2, "best": 4},
        supports_guidance=False,
        default_guidance=0.0
    ),
    "sdxl-turbo": ModelConfig(
        name="sdxl-turbo",
        display_name="SDXL-Turbo",
        description="Fast high-quality generation (1024x1024)",
        min_vram_gb=6.0,
        max_resolution=1024,
        optimal_steps={"fast": 1, "medium": 2, "best": 4},
        supports_guidance=False,
        default_guidance=0.0
    ),
    "sdxl-lightning": ModelConfig(
        name="sdxl-lightning",
        display_name="SDXL-Lightning",
        description="Lightning-fast SDXL with LoRA (1024x1024)",
        min_vram_gb=6.0,
        max_resolution=1024,
        optimal_steps={"fast": 2, "medium": 4, "best": 8},
        supports_guidance=True,
        default_guidance=7.5
    )
}

def get_gpu_info():
    """Get GPU information"""
    if not torch.cuda.is_available():
        return None, 0.0
    
    gpu_name = torch.cuda.get_device_name(0)
    total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    return gpu_name, total_memory

def load_model(model_name: str):
    """Load and optimize model"""
    global current_model, pipelines
    
    if model_name in pipelines:
        current_model = model_name
        return pipelines[model_name]
    
    logger.info(f"Loading model: {model_name}")
    
    try:
        if model_name == "sd-turbo":
            pipeline = AutoPipelineForText2Image.from_pretrained(
                "stabilityai/sd-turbo",
                torch_dtype=torch.float16,
                variant="fp16",
                use_safetensors=True
            )
        
        elif model_name == "sdxl-turbo":
            pipeline = AutoPipelineForText2Image.from_pretrained(
                "stabilityai/sdxl-turbo",
                torch_dtype=torch.float16,
                variant="fp16",
                use_safetensors=True
            )
        
        elif model_name == "sdxl-lightning":
            # Load base SDXL and Lightning LoRA
            pipeline = StableDiffusionXLPipeline.from_pretrained(
                "stabilityai/stable-diffusion-xl-base-1.0",
                torch_dtype=torch.float16,
                variant="fp16",
                use_safetensors=True
            )
            pipeline.load_lora_weights("ByteDance/SDXL-Lightning", weight_name="sdxl_lightning_4step_lora.safetensors")
        
        else:
            raise ValueError(f"Unknown model: {model_name}")
        
        # Apply optimizations
        pipeline.enable_vae_slicing()
        pipeline.enable_attention_slicing()
        
        # Move to GPU
        pipeline = pipeline.to("cuda")
        
        pipelines[model_name] = pipeline
        current_model = model_name
        
        logger.info(f"Model {model_name} loaded successfully")
        return pipeline
        
    except Exception as e:
        logger.error(f"Failed to load model {model_name}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to load model: {str(e)}")

@app.get("/", response_model=Dict[str, str])
async def root():
    """API root endpoint"""
    return {
        "message": "Ultra-Fast Image Generator API",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/system-info", response_model=SystemInfo)
async def get_system_info():
    """Get system information and available models"""
    gpu_name, total_vram = get_gpu_info()
    
    if not gpu_name:
        raise HTTPException(status_code=500, detail="No CUDA GPU available")
    
    # Filter models based on available VRAM
    available_models = []
    for model_config in MODEL_CONFIGS.values():
        if total_vram >= model_config.min_vram_gb:
            available_models.append(model_config)
    
    return SystemInfo(
        gpu_name=gpu_name,
        total_vram_gb=total_vram,
        available_models=available_models,
        current_model=current_model
    )

@app.get("/models", response_model=List[ModelConfig])
async def get_models():
    """Get available model configurations"""
    gpu_name, total_vram = get_gpu_info()
    
    if not gpu_name:
        raise HTTPException(status_code=500, detail="No CUDA GPU available")
    
    # Filter models based on available VRAM
    available_models = []
    for model_config in MODEL_CONFIGS.values():
        if total_vram >= model_config.min_vram_gb:
            available_models.append(model_config)
    
    return available_models

@app.post("/generate", response_model=GenerationResponse)
async def generate_image(config: GenerationConfig):
    """Generate image with given configuration"""
    try:
        # Load model if needed
        pipeline = load_model(config.model_name)
        
        # Clear GPU cache
        torch.cuda.empty_cache()
        
        # Generate unique ID for this generation
        image_id = str(uuid.uuid4())
        
        # Start timing
        start_time = time.time()
        
        # Prepare generation parameters
        gen_params = {
            "prompt": config.prompt,
            "num_inference_steps": config.num_inference_steps,
            "height": config.height,
            "width": config.width,
        }
        
        # Add guidance scale if model supports it
        model_config = MODEL_CONFIGS[config.model_name]
        if model_config.supports_guidance:
            gen_params["guidance_scale"] = config.guidance_scale
        else:
            gen_params["guidance_scale"] = 0.0
        
        # Add negative prompt if provided
        if config.negative_prompt:
            gen_params["negative_prompt"] = config.negative_prompt
        
        # Set seed if provided
        if config.seed is not None:
            gen_params["generator"] = torch.Generator(device="cuda").manual_seed(config.seed)
        
        # Generate image
        with torch.inference_mode():
            result = pipeline(**gen_params)
            image = result.images[0]
        
        # Calculate metrics
        generation_time = time.time() - start_time
        vram_usage = torch.cuda.max_memory_allocated() / 1024**3
        
        # Save image
        os.makedirs("generated_images", exist_ok=True)
        image_path = f"generated_images/{image_id}.png"
        image.save(image_path)
        
        logger.info(f"Generated image {image_id} in {generation_time:.2f}s using {vram_usage:.2f}GB VRAM")
        
        return GenerationResponse(
            success=True,
            image_id=image_id,
            generation_time=generation_time,
            vram_usage=vram_usage,
            config_used=config
        )
        
    except Exception as e:
        logger.error(f"Generation failed: {e}")
        return GenerationResponse(
            success=False,
            image_id="",
            generation_time=0.0,
            vram_usage=0.0,
            config_used=config,
            error_message=str(e)
        )

@app.get("/image/{image_id}")
async def get_image(image_id: str):
    """Download generated image"""
    image_path = f"generated_images/{image_id}.png"
    
    if not os.path.exists(image_path):
        raise HTTPException(status_code=404, detail="Image not found")
    
    return FileResponse(
        image_path,
        media_type="image/png",
        filename=f"generated_{image_id}.png"
    )

if __name__ == "__main__":
    # import uvicorn
    # uvicorn.run(app, host="0.0.0.0", port=8000)
    app.run(host="0.0.0.0", port=8000)
