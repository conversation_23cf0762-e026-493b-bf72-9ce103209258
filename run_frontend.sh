#!/bin/bash

# Ultra-Fast Image Generator - Frontend Startup Script
# This script starts the Streamlit web application using uv

echo "🎨 Starting Ultra-Fast Image Generator Web App..."
echo "🌐 Web app will be available at: http://localhost:8501"
echo "🔄 Press Ctrl+C to stop the app"
echo "=================================================="

# Check if uv is installed
if ! command -v uv &> /dev/null; then
    echo "❌ uv is not installed. Please install it first:"
    echo "   curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

# Check if backend is running
echo "🔍 Checking if backend is running..."
if curl -s http://localhost:8000/ > /dev/null 2>&1; then
    echo "✅ Backend is running"
else
    echo "⚠️  Backend not detected at http://localhost:8000"
    echo "   Please start the backend first with: ./run_backend.sh"
    echo "   Or run both with: ./run_all.sh"
    echo ""
    echo "Continuing anyway..."
fi

# Install dependencies if needed
echo "📦 Installing dependencies..."
uv sync

# Start the Streamlit app
echo "🎨 Starting Streamlit app..."
uv run streamlit run app.py --server.port 8501 --server.address 0.0.0.0 --browser.gatherUsageStats false
