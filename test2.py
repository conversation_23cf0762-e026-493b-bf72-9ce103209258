#!/usr/bin/env python3
"""
Ultra-Fast Image Generator for Low VRAM GPUs
Optimized for maximum speed on 4-6GB VRAM consumer GPUs
"""

import torch
import time
from datetime import datetime
from diffusers import AutoPipelineForText2Image

def setup_pipeline():
    """Initialize the fastest model with maximum optimizations"""
    print("🚀 Initializing ultra-fast image generator...")
    print("📊 Checking GPU availability...")

    if not torch.cuda.is_available():
        print("❌ CUDA not available. This script requires a GPU.")
        return None

    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    print(f"🎮 GPU: {torch.cuda.get_device_name(0)}")
    print(f"💾 VRAM: {gpu_memory:.1f} GB")

    try:
        # Use SD-Turbo for maximum speed (works on 4GB+ VRAM)
        print("⚡ Loading SD-Turbo (fastest model)...")
        pipeline = AutoPipelineForText2Image.from_pretrained(
            "stabilityai/sd-turbo",
            torch_dtype=torch.float16,
            variant="fp16",
            use_safetensors=True
        )

        # Apply all memory optimizations
        print("🔧 Applying memory optimizations...")
        pipeline.enable_model_cpu_offload()  # Offload to CPU when not in use
        pipeline.enable_vae_slicing()        # Process VAE in slices
        pipeline.enable_attention_slicing()  # Reduce attention memory

        # Enable memory efficient attention if available
        try:
            pipeline.enable_xformers_memory_efficient_attention()
            print("✅ xFormers memory optimization enabled")
        except:
            print("⚠️  xFormers not available, using default attention")

        # Compile for extra speed (PyTorch 2.0+)
        try:
            pipeline.unet = torch.compile(pipeline.unet, mode="reduce-overhead", fullgraph=True)
            print("✅ Model compiled for extra speed")
        except:
            print("⚠️  Compilation not available, using default mode")

        print("✅ Pipeline ready! Ultra-fast generation enabled.")
        return pipeline

    except Exception as e:
        print(f"❌ Error loading SD-Turbo: {e}")
        print("🔄 Falling back to alternative model...")

        try:
            # Fallback to SDXL-Turbo if SD-Turbo fails
            print("⚡ Loading SDXL-Turbo as fallback...")
            pipeline = AutoPipelineForText2Image.from_pretrained(
                "stabilityai/sdxl-turbo",
                torch_dtype=torch.float16,
                variant="fp16",
                use_safetensors=True
            )
            pipeline.enable_model_cpu_offload()
            pipeline.enable_vae_slicing()
            pipeline.enable_attention_slicing()
            print("✅ SDXL-Turbo loaded successfully")
            return pipeline

        except Exception as e2:
            print(f"❌ Error loading fallback model: {e2}")
            return None

def generate_image(pipeline, prompt, filename=None):
    """Generate image with maximum speed settings"""
    if pipeline is None:
        print("❌ No pipeline available")
        return None

    print(f"\n🎨 Generating: '{prompt}'")
    print("⚡ Using 1-step ultra-fast generation...")

    # Clear GPU cache
    torch.cuda.empty_cache()

    # Start timing
    start_time = time.time()

    try:
        # Generate with minimal steps for maximum speed
        with torch.inference_mode():
            image = pipeline(
                prompt=prompt,
                num_inference_steps=1,      # Ultra-fast: just 1 step
                guidance_scale=0.0,         # No guidance for speed
                height=512,                 # Optimal size for speed
                width=512,
                generator=torch.Generator().manual_seed(42)  # Reproducible results
            ).images[0]

        # Calculate timing and memory
        generation_time = time.time() - start_time
        memory_used = torch.cuda.max_memory_allocated() / 1024**3

        # Save image
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"generated_{timestamp}.png"

        image.save(filename)

        # Print results
        print(f"✅ Generated in {generation_time:.2f} seconds")
        print(f"💾 Peak VRAM: {memory_used:.2f} GB")
        print(f"💾 Saved as: {filename}")

        return image, generation_time, memory_used

    except Exception as e:
        print(f"❌ Generation failed: {e}")
        return None

def main():
    """Main interactive loop"""
    print("=" * 60)
    print("🚀 ULTRA-FAST IMAGE GENERATOR")
    print("⚡ Optimized for Low VRAM GPUs (4-6GB)")
    print("=" * 60)

    # Initialize pipeline
    pipeline = setup_pipeline()
    if pipeline is None:
        print("❌ Failed to initialize. Please check your GPU setup.")
        return

    print("\n🎯 Ready for ultra-fast generation!")
    print("💡 Tips: Keep prompts simple for best speed")
    print("📝 Type 'quit' to exit\n")

    while True:
        try:
            # Get user input
            prompt = input("🎨 Enter your prompt: ").strip()

            if prompt.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break

            if not prompt:
                print("⚠️  Please enter a prompt")
                continue

            # Generate image
            result = generate_image(pipeline, prompt)

            if result:
                print("🎉 Generation complete!\n")
            else:
                print("❌ Generation failed. Try a different prompt.\n")

        except KeyboardInterrupt:
            print("\n👋 Interrupted by user. Goodbye!")
            break
        except Exception as e:
            print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()