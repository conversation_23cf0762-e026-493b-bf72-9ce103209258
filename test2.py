from diffusers import StableDiffusionXLPipeline, DiffusionPipeline, AutoPipelineForText2Image
import torch
from diffusers.utils import load_image
import time

def test_model_performance(pipeline, model_name, prompt="A beautiful landscape", steps=4):
    """Test and time model performance"""
    print(f"\n=== Testing {model_name} ===")

    # Clear cache
    torch.cuda.empty_cache()

    # Warmup
    _ = pipeline(prompt, num_inference_steps=steps, guidance_scale=0.0 if "turbo" in model_name.lower() else 7.5)

    # Time generation
    start_time = time.time()
    image = pipeline(prompt, num_inference_steps=steps, guidance_scale=0.0 if "turbo" in model_name.lower() else 7.5).images[0]
    end_time = time.time()

    # Memory usage
    memory_used = torch.cuda.max_memory_allocated() / 1024**3

    print(f"Generation time: {end_time - start_time:.2f} seconds")
    print(f"Peak VRAM usage: {memory_used:.2f} GB")
    print(f"Steps used: {steps}")

    # Save image
    image.save(f"{model_name.replace('/', '_').replace('-', '_')}_output.png")
    print(f"Image saved as: {model_name.replace('/', '_').replace('-', '_')}_output.png")

    return image, end_time - start_time, memory_used

# Test different fast models for low VRAM

print("Testing fast image generation models for low VRAM GPUs...")
prompt = "A serene mountain landscape with a crystal clear lake, photorealistic, 8k"

# 1. SD-Turbo (Fastest, lowest VRAM)
print("\n" + "="*50)
print("1. SD-Turbo - Ultra fast, 4GB VRAM")
try:
    pipeline_sd_turbo = AutoPipelineForText2Image.from_pretrained(
        "stabilityai/sd-turbo",
        torch_dtype=torch.float16,
        variant="fp16"
    )
    pipeline_sd_turbo.enable_model_cpu_offload()
    pipeline_sd_turbo.enable_vae_slicing()

    test_model_performance(pipeline_sd_turbo, "SD-Turbo", prompt, steps=1)

except Exception as e:
    print(f"Error with SD-Turbo: {e}")

# 2. SDXL-Turbo (Fast, 6GB VRAM)
print("\n" + "="*50)
print("2. SDXL-Turbo - Fast, higher quality")
try:
    pipeline_sdxl_turbo = AutoPipelineForText2Image.from_pretrained(
        "stabilityai/sdxl-turbo",
        torch_dtype=torch.float16,
        variant="fp16"
    )
    pipeline_sdxl_turbo.enable_model_cpu_offload()
    pipeline_sdxl_turbo.enable_vae_slicing()

    test_model_performance(pipeline_sdxl_turbo, "SDXL-Turbo", prompt, steps=1)

except Exception as e:
    print(f"Error with SDXL-Turbo: {e}")

# 3. SDXL-Lightning (Correct way to load)
print("\n" + "="*50)
print("3. SDXL-Lightning - Using LoRA approach")
try:
    # Load base SDXL model
    pipeline_lightning = StableDiffusionXLPipeline.from_pretrained(
        "stabilityai/stable-diffusion-xl-base-1.0",
        torch_dtype=torch.float16,
        variant="fp16"
    )

    # Load Lightning LoRA
    pipeline_lightning.load_lora_weights("ByteDance/SDXL-Lightning", weight_name="sdxl_lightning_4step_lora.safetensors")
    pipeline_lightning.enable_model_cpu_offload()
    pipeline_lightning.enable_vae_slicing()

    test_model_performance(pipeline_lightning, "SDXL-Lightning", prompt, steps=4)

except Exception as e:
    print(f"Error with SDXL-Lightning: {e}")

# 4. LCM-LoRA (Very fast alternative)
print("\n" + "="*50)
print("4. LCM-LoRA SDXL - Fast with good quality")
try:
    pipeline_lcm = StableDiffusionXLPipeline.from_pretrained(
        "stabilityai/stable-diffusion-xl-base-1.0",
        torch_dtype=torch.float16,
        variant="fp16"
    )

    # Load LCM LoRA
    pipeline_lcm.load_lora_weights("latent-consistency/lcm-lora-sdxl")
    pipeline_lcm.enable_model_cpu_offload()
    pipeline_lcm.enable_vae_slicing()

    # LCM requires specific scheduler
    from diffusers import LCMScheduler
    pipeline_lcm.scheduler = LCMScheduler.from_config(pipeline_lcm.scheduler.config)

    test_model_performance(pipeline_lcm, "LCM-LoRA-SDXL", prompt, steps=4)

except Exception as e:
    print(f"Error with LCM-LoRA: {e}")

print("\n" + "="*50)
print("Testing complete! Check the generated images.")
print("\nMemory optimization tips:")
print("- Use enable_model_cpu_offload() for lower VRAM usage")
print("- Use enable_vae_slicing() for batch processing")
print("- Use enable_vae_tiling() for high-resolution images")
print("- Consider torch.compile() for additional speedup")