[project]
name = "ultra-fast-image-generator"
version = "1.0.0"
description = "Ultra-fast image generation optimized for low VRAM GPUs"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "accelerate>=1.9.0",
    "diffusers>=0.34.0",
    "fastapi[standard]>=0.116.1",
    "torch>=2.7.1",
    "transformers>=4.53.2",
    "streamlit>=1.28.1",
    "torchvision>=0.15.0",
    "safetensors>=0.4.0",
    "Pillow>=9.5.0",
    "requests>=2.31.0",
    "pydantic>=2.4.0",
    "python-multipart>=0.0.6",
    "xformers>=0.0.20",
]

[project.optional-dependencies]
dev = [
    "black",
    "isort",
    "flake8",
    "pytest",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py312']

[tool.isort]
profile = "black"
